 Task :app:compileDebugKotlin
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:643:11 Argument type mismatch: actual type is 'kotlin.coroutines.SuspendFunction7<kotlin.Array<T>, ERROR CLASS: Cannot infer type for parameter completionsMap, ERROR CLASS: Cannot infer type for parameter completionValuesMap, ERROR CLASS: Cannot infer type for parameter dialogState, ERROR CLASS: Cannot infer type for parameter firstDayOfWeek, ERROR CLASS: Cannot infer type for parameter sortType, ERROR CLASS: Cannot infer type for parameter customOrder, R>', but 'kotlin.coroutines.SuspendFunction1<kotlin.Array<T>, R>' was expected.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:643:24 Cannot infer type for this parameter. Please specify it explicitly.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:643:40 Cannot infer type for this parameter. Please specify it explicitly.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:643:61 Cannot infer type for this parameter. Please specify it explicitly.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:643:74 Cannot infer type for this parameter. Please specify it explicitly.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:643:90 Cannot infer type for this parameter. Please specify it explicitly.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:643:100 Cannot infer type for this parameter. Please specify it explicitly.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:644:27 Unresolved reference 'habitsWithCompletions'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:645:27 Unresolved reference 'copy'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:647:56 Unresolved reference 'showMeasurableDialog'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:648:59 Unresolved reference 'measurableDialogHabitId'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:649:56 Unresolved reference 'measurableDialogDate'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:650:61 Unresolved reference 'measurableDialogHabitName'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:651:56 Unresolved reference 'measurableDialogUnit'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:652:64 Unresolved reference 'measurableDialogCurrentValue'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:653:63 Unresolved reference 'measurableDialogTargetValue'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:654:62 Unresolved reference 'measurableDialogTargetType'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:658:48 Unresolved reference 'habitsWithCompletions'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:658:76 Cannot infer type for this parameter. Please specify it explicitly.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:659:75 Unresolved reference 'habit'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:659:88 Not enough information to infer type argument for 'K'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:659:88 Not enough information to infer type argument for 'V'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:660:85 Unresolved reference 'habit'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:660:98 Not enough information to infer type argument for 'K'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:660:98 Not enough information to infer type argument for 'V'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:663:90 Unresolved reference 'habit'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:663:124 Unresolved reference 'habit'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:664:82 Unresolved reference 'keys'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:665:93 Unresolved reference 'keys'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:668:46 Unresolved reference 'habit'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:669:35 Unresolved reference 'weekInfo'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:672:46 Unresolved reference 'habit'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:673:25 Argument type mismatch: actual type is 'kotlin.Any', but 'kotlin.collections.Map<kotlin.Long, kotlin.Boolean>' was expected.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:674:25 Argument type mismatch: actual type is 'kotlin.Any', but 'kotlin.collections.Map<kotlin.Long, kotlin.String>' was expected.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:675:35 Unresolved reference 'weekInfo'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:677:42 Unresolved reference 'copy'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:686:60 Argument type mismatch: actual type is 'kotlinx.coroutines.flow.Flow<ERROR CLASS: Cannot infer argument for type parameter R>', but 'kotlin.collections.List<com.example.habits9.ui.HabitWithCompletions>' was expected.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:694:27 Unresolved reference 'copy'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:697:56 Unresolved reference 'showMeasurableDialog'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:698:59 Unresolved reference 'measurableDialogHabitId'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:699:56 Unresolved reference 'measurableDialogDate'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:700:61 Unresolved reference 'measurableDialogHabitName'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:701:56 Unresolved reference 'measurableDialogUnit'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:702:64 Unresolved reference 'measurableDialogCurrentValue'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:703:63 Unresolved reference 'measurableDialogTargetValue'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/MainViewModel.kt:704:62 Unresolved reference 'measurableDialogTargetType'.

> Task :app:compileDebugKotlin FAILED

FAILURE: Build failed with an exception.

* What went wrong:
Execution failed for task ':app:compileDebugKotlin'.
> A failure occurred while executing org.jetbrains.kotlin.compilerRunner.GradleCompilerRunnerWithWorkers$GradleKotlinCompilerWorkAction
   > Compilation error. See log for more details

* Try:
> Run with --stacktrace option to get the stack trace.
> Run with --info or --debug option to get more log output.
> Run with --scan to get full insights.
> Get more help at https://help.gradle.org.

BUILD FAILED in 46s
30 actionable tasks: 3 executed, 4 from cache, 23 up-to-date