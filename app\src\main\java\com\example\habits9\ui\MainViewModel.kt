package com.example.habits9.ui

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.habits9.data.Completion
import com.example.habits9.data.CompletionRepository
import com.example.habits9.data.Habit
import com.example.habits9.data.HabitRepository
import com.example.habits9.data.HabitSection
import com.example.habits9.data.HabitSectionRepository
import com.example.habits9.data.HabitSortType
import com.example.habits9.data.HabitType
import com.example.habits9.data.NumericalHabitType
import com.example.habits9.utils.HabitScheduler
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import java.time.DayOfWeek
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.time.temporal.TemporalAdjusters
import java.time.temporal.WeekFields
import java.time.DayOfWeek as JavaDayOfWeek
import javax.inject.Inject

/**
 * Represents a habit with its completion status for the current week
 */
data class HabitWithCompletions(
    val habit: Habit,
    val completions: Map<Long, Boolean> = emptyMap(), // timestamp -> isCompleted (for Yes/No habits)
    val completionValues: Map<Long, String> = emptyMap(), // timestamp -> value (for measurable habits)
    val currentStreak: Int = 0,
    val scheduledDays: Map<Long, Boolean> = emptyMap() // timestamp -> isScheduled (for smart display)
)

data class WeekInfo(
    val dates: List<LocalDate> = emptyList(),
    val timestamps: List<Long> = emptyList(),
    val formattedDates: List<String> = emptyList(),
    val formattedDayAbbreviations: List<String> = emptyList(),
    val dailyCompletionPercentages: List<Float> = emptyList(),
    val weeklyCompletionPercentage: Float = 0f,
    val weekNumber: Int = 0,
    val firstDayOfWeek: String = "SUNDAY"
)

data class MainUiState(
    val habitsWithCompletions: List<HabitWithCompletions> = emptyList(),
    val isLoading: Boolean = false,
    val currentWeekStart: Long = 0L,
    val weekInfo: WeekInfo = WeekInfo(),
    val showMeasurableDialog: Boolean = false,
    val measurableDialogHabitId: Long = 0L,
    val measurableDialogDate: Long = 0L,
    val measurableDialogHabitName: String = "",
    val measurableDialogUnit: String = "",
    val measurableDialogCurrentValue: String = "",
    val measurableDialogTargetValue: Double = 0.0,
    val measurableDialogTargetType: String = "at least",
    val currentSortType: HabitSortType = HabitSortType.CUSTOM_ORDER
)

@HiltViewModel
class MainViewModel @Inject constructor(
    private val habitRepository: HabitRepository,
    private val completionRepository: CompletionRepository,
    private val userPreferencesRepository: com.example.habits9.data.UserPreferencesRepository,
    private val habitSectionRepository: HabitSectionRepository
) : ViewModel() {

    // Dialog state for measurable habits
    private val _dialogState = MutableStateFlow(
        MainUiState().copy(
            showMeasurableDialog = false,
            measurableDialogHabitId = 0L,
            measurableDialogDate = 0L,
            measurableDialogHabitName = "",
            measurableDialogUnit = "",
            measurableDialogCurrentValue = "",
            measurableDialogTargetValue = 0.0,
            measurableDialogTargetType = "at least"
        )
    )

    /**
     * Utility object for week boundary calculations
     */
    object WeekBoundaryUtils {
        /**
         * Get the start of the week for a given date based on the first day of week preference
         * @param date The date to find the week start for
         * @param firstDayOfWeek "SUNDAY" or "MONDAY"
         * @return LocalDate representing the start of the week
         */
        fun getWeekStart(date: LocalDate, firstDayOfWeek: String): LocalDate {
            val startDay = if (firstDayOfWeek == "SUNDAY") JavaDayOfWeek.SUNDAY else JavaDayOfWeek.MONDAY
            return date.with(TemporalAdjusters.previousOrSame(startDay))
        }

        /**
         * Get the end of the week for a given date based on the first day of week preference
         * @param date The date to find the week end for
         * @param firstDayOfWeek "SUNDAY" or "MONDAY"
         * @return LocalDate representing the end of the week
         */
        fun getWeekEnd(date: LocalDate, firstDayOfWeek: String): LocalDate {
            val endDay = if (firstDayOfWeek == "SUNDAY") JavaDayOfWeek.SATURDAY else JavaDayOfWeek.SUNDAY
            return date.with(TemporalAdjusters.nextOrSame(endDay))
        }

        /**
         * Get the week number for a given date based on the first day of week preference
         * @param date The date to get the week number for
         * @param firstDayOfWeek "SUNDAY" or "MONDAY"
         * @return Int representing the week number of the year
         */
        fun getWeekNumber(date: LocalDate, firstDayOfWeek: String): Int {
            val startDay = if (firstDayOfWeek == "SUNDAY") JavaDayOfWeek.SUNDAY else JavaDayOfWeek.MONDAY
            val weekFields = WeekFields.of(startDay, 1)
            return date.get(weekFields.weekOfYear())
        }

        /**
         * Get all dates in the current week based on the first day of week preference
         * @param date The reference date (usually today)
         * @param firstDayOfWeek "SUNDAY" or "MONDAY"
         * @return List<LocalDate> containing all 7 days of the week
         */
        fun getCurrentWeekDates(date: LocalDate, firstDayOfWeek: String): List<LocalDate> {
            val weekStart = getWeekStart(date, firstDayOfWeek)
            return (0..6).map { weekStart.plusDays(it.toLong()) }
        }
    }

    /**
     * Calculate which days a habit is scheduled for based on its frequency rules
     * FIXED: Use the same timestamp normalization as the UI to ensure key consistency
     */
    private fun calculateScheduledDays(
        habit: com.example.habits9.data.Habit,
        timestamps: List<Long>
    ): Map<Long, Boolean> {
        return timestamps.associateWith { timestamp ->
            // BUGFIX: Use timezone-aware conversion to match the UI timestamp generation
            // The original code used LocalDate.ofEpochDay() which is UTC-based and can cause
            // day offset issues in non-UTC timezones. This was causing scheduled days to appear
            // one day ahead (Monday scheduled → Tuesday enabled).
            val instant = Instant.ofEpochMilli(timestamp)
            val date = instant.atZone(ZoneId.systemDefault()).toLocalDate()

            // Calculate if the habit is scheduled for this date
            HabitScheduler.isHabitScheduled(habit, date)
        }.mapKeys { (timestamp, _) ->
            // Use the normalized dayStart as the key to match UI lookup
            val dayLength = 24 * 60 * 60 * 1000L
            (timestamp / dayLength) * dayLength
        }
    }

    /**
     * Filter habits to only show those that are scheduled for at least one day
     * in the visible time range (smart habit display)
     */
    private fun filterScheduledHabits(
        habitsWithCompletions: List<HabitWithCompletions>
    ): List<HabitWithCompletions> {
        return habitsWithCompletions.filter { habitWithCompletions ->
            // Show habit if it's scheduled for at least one day in the visible range
            habitWithCompletions.scheduledDays.values.any { it }
        }
    }

    /**
     * Calculate weekly completion percentage for a habit
     * Formula: (Total Completions in Week / Total Opportunities in Week) * 100
     */
    fun calculateWeeklyPercentage(
        habit: Habit,
        completions: Map<Long, Boolean>,
        completionValues: Map<Long, String>,
        weekStartDate: LocalDate
    ): Float {
        val weekDates = (0..6).map { weekStartDate.plusDays(it.toLong()) }
        val weekTimestamps = weekDates.map { date ->
            date.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli()
        }

        var totalOpportunities = 0
        var totalCompletions = 0

        weekTimestamps.forEach { timestamp ->
            val date = LocalDate.ofEpochDay(timestamp / (24 * 60 * 60 * 1000))
            val isScheduled = HabitScheduler.isHabitScheduled(habit, date)

            if (isScheduled) {
                totalOpportunities++

                val isCompleted = if (habit.habitType == HabitType.NUMERICAL) {
                    val valueString = completionValues[timestamp]
                    if (valueString.isNullOrEmpty()) {
                        false
                    } else {
                        val value = valueString.toDoubleOrNull() ?: 0.0
                        when (habit.numericalHabitType) {
                            NumericalHabitType.AT_LEAST -> value >= habit.targetValue
                            NumericalHabitType.AT_MOST -> value <= habit.targetValue
                        }
                    }
                } else {
                    completions[timestamp] == true
                }

                if (isCompleted) totalCompletions++
            }
        }

        return if (totalOpportunities > 0) {
            (totalCompletions.toFloat() / totalOpportunities) * 100f
        } else {
            0f
        }
    }

    /**
     * Calculate monthly completion percentage for a habit
     * Formula: (Total Completions in Month / Total Opportunities in Month) * 100
     */
    fun calculateMonthlyPercentage(
        habit: Habit,
        completions: Map<Long, Boolean>,
        completionValues: Map<Long, String>,
        month: Int,
        year: Int
    ): Float {
        val firstDayOfMonth = LocalDate.of(year, month, 1)
        val lastDayOfMonth = firstDayOfMonth.withDayOfMonth(firstDayOfMonth.lengthOfMonth())

        var totalOpportunities = 0
        var totalCompletions = 0
        var currentDate = firstDayOfMonth

        while (!currentDate.isAfter(lastDayOfMonth)) {
            val timestamp = currentDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli()
            val isScheduled = HabitScheduler.isHabitScheduled(habit, currentDate)

            if (isScheduled) {
                totalOpportunities++

                val isCompleted = if (habit.habitType == HabitType.NUMERICAL) {
                    val valueString = completionValues[timestamp]
                    if (valueString.isNullOrEmpty()) {
                        false
                    } else {
                        val value = valueString.toDoubleOrNull() ?: 0.0
                        when (habit.numericalHabitType) {
                            NumericalHabitType.AT_LEAST -> value >= habit.targetValue
                            NumericalHabitType.AT_MOST -> value <= habit.targetValue
                        }
                    }
                } else {
                    completions[timestamp] == true
                }

                if (isCompleted) totalCompletions++
            }

            currentDate = currentDate.plusDays(1)
        }

        return if (totalOpportunities > 0) {
            (totalCompletions.toFloat() / totalOpportunities) * 100f
        } else {
            0f
        }
    }

    /**
     * Calculate yearly completion percentage for a habit
     * Formula: (Total Completions in Year / Total Opportunities in Year) * 100
     */
    fun calculateYearlyPercentage(
        habit: Habit,
        completions: Map<Long, Boolean>,
        completionValues: Map<Long, String>,
        year: Int
    ): Float {
        val firstDayOfYear = LocalDate.of(year, 1, 1)
        val lastDayOfYear = LocalDate.of(year, 12, 31)

        var totalOpportunities = 0
        var totalCompletions = 0
        var currentDate = firstDayOfYear

        while (!currentDate.isAfter(lastDayOfYear)) {
            val timestamp = currentDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli()
            val isScheduled = HabitScheduler.isHabitScheduled(habit, currentDate)

            if (isScheduled) {
                totalOpportunities++

                val isCompleted = if (habit.habitType == HabitType.NUMERICAL) {
                    val valueString = completionValues[timestamp]
                    if (valueString.isNullOrEmpty()) {
                        false
                    } else {
                        val value = valueString.toDoubleOrNull() ?: 0.0
                        when (habit.numericalHabitType) {
                            NumericalHabitType.AT_LEAST -> value >= habit.targetValue
                            NumericalHabitType.AT_MOST -> value <= habit.targetValue
                        }
                    }
                } else {
                    completions[timestamp] == true
                }

                if (isCompleted) totalCompletions++
            }

            currentDate = currentDate.plusDays(1)
        }

        return if (totalOpportunities > 0) {
            (totalCompletions.toFloat() / totalOpportunities) * 100f
        } else {
            0f
        }
    }

    /**
     * Calculate current streak for a habit based on completion history
     * Now considers only scheduled days - a streak is broken only when a habit
     * was scheduled but not completed. Non-scheduled days are ignored.
     */
    private fun calculateCurrentStreak(
        habit: com.example.habits9.data.Habit,
        completions: Map<Long, Boolean>,
        completionValues: Map<Long, String>,
        timestamps: List<Long>
    ): Int {
        var streak = 0
        val today = LocalDate.now()

        // Start from today and go backwards
        var currentDate = today
        while (true) {
            val currentTimestamp = currentDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli()
            val dayStart = (currentTimestamp / (24 * 60 * 60 * 1000L)) * (24 * 60 * 60 * 1000L)

            // Check if the habit is scheduled for this day
            val isScheduled = HabitScheduler.isHabitScheduled(habit, currentDate)

            if (!isScheduled) {
                // Skip non-scheduled days - they don't affect the streak
                currentDate = currentDate.minusDays(1)
                continue
            }

            val isSuccessfulDay = if (habit.habitType == com.example.habits9.data.HabitType.NUMERICAL) {
                // For measurable habits, check if target is met
                val valueString = completionValues[dayStart]
                if (valueString.isNullOrEmpty()) {
                    false
                } else {
                    val value = valueString.toDoubleOrNull() ?: 0.0
                    when (habit.numericalHabitType) {
                        com.example.habits9.data.NumericalHabitType.AT_LEAST ->
                            value >= habit.targetValue
                        com.example.habits9.data.NumericalHabitType.AT_MOST ->
                            value <= habit.targetValue
                    }
                }
            } else {
                // For Yes/No habits, use boolean completion
                completions[dayStart] == true
            }

            if (isSuccessfulDay) {
                streak++
                currentDate = currentDate.minusDays(1)
            } else {
                // Streak is broken - habit was scheduled but not completed
                break
            }
        }
        return streak
    }

    /**
     * Calculate daily completion percentages for the week
     * Now considers only scheduled habits - percentage is based on
     * (completed scheduled habits / total scheduled habits) for each day
     */
    private fun calculateDailyCompletionPercentages(
        habitsWithCompletions: List<HabitWithCompletions>,
        timestamps: List<Long>
    ): List<Float> {
        if (habitsWithCompletions.isEmpty()) return timestamps.map { 0f }

        return timestamps.map { timestamp ->
            val dayStart = (timestamp / (24 * 60 * 60 * 1000L)) * (24 * 60 * 60 * 1000L)

            // Only count habits that are scheduled for this day
            val scheduledHabits = habitsWithCompletions.filter { habitWithCompletions ->
                habitWithCompletions.scheduledDays[dayStart] == true
            }

            if (scheduledHabits.isEmpty()) return@map 0f

            val completedScheduledHabits = scheduledHabits.count { habitWithCompletions ->
                if (habitWithCompletions.habit.habitType == HabitType.NUMERICAL) {
                    // For measurable habits, check if the value meets the target
                    val valueString = habitWithCompletions.completionValues[dayStart]
                    if (valueString.isNullOrEmpty()) {
                        false
                    } else {
                        val value = valueString.toDoubleOrNull() ?: 0.0
                        when (habitWithCompletions.habit.numericalHabitType) {
                            com.example.habits9.data.NumericalHabitType.AT_LEAST ->
                                value >= habitWithCompletions.habit.targetValue
                            com.example.habits9.data.NumericalHabitType.AT_MOST ->
                                value <= habitWithCompletions.habit.targetValue
                        }
                    }
                } else {
                    // For Yes/No habits, check boolean completion
                    habitWithCompletions.completions[dayStart] == true
                }
            }
            completedScheduledHabits.toFloat() / scheduledHabits.size.toFloat()
        }
    }

    /**
     * Calculate date range information for the last 15 days (today + 14 previous days)
     * Returns dates in reverse chronological order (today first, then yesterday, etc.)
     * Also calculates the current week completion percentage based on the user's firstDayOfWeek setting
     */
    private fun calculateWeekInfo(firstDayOfWeek: String, habitsWithCompletions: List<HabitWithCompletions> = emptyList()): WeekInfo {
        val today = LocalDate.now()
        val formatterDayOfMonth = DateTimeFormatter.ofPattern("dd")

        // Generate the last 15 days (today + 14 previous days) in reverse chronological order
        // Today appears first (rightmost), then yesterday, etc.
        val dates = (0..14).map { daysBack ->
            today.minusDays(daysBack.toLong())
        }

        // Convert to timestamps
        val timestamps = dates.map { date ->
            date.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli()
        }

        // Format dates for display (just the day number)
        val formattedDates = dates.map { date ->
            date.format(formatterDayOfMonth)
        }

        // Format day abbreviations separately (two-letter uppercase format as required)
        val formattedDayAbbreviations = dates.map { date ->
            when (date.dayOfWeek) {
                DayOfWeek.SUNDAY -> "SU"
                DayOfWeek.MONDAY -> "MO"
                DayOfWeek.TUESDAY -> "TU"
                DayOfWeek.WEDNESDAY -> "WE"
                DayOfWeek.THURSDAY -> "TH"
                DayOfWeek.FRIDAY -> "FR"
                DayOfWeek.SATURDAY -> "SA"
            }
        }

        // Calculate daily completion percentages for all 15 days
        val dailyCompletionPercentages = calculateDailyCompletionPercentages(habitsWithCompletions, timestamps)

        // Calculate the current week completion percentage based on firstDayOfWeek setting
        val currentWeekDates = WeekBoundaryUtils.getCurrentWeekDates(today, firstDayOfWeek)
        val currentWeekTimestamps = currentWeekDates.map { date ->
            date.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli()
        }
        val currentWeekCompletionPercentages = calculateDailyCompletionPercentages(habitsWithCompletions, currentWeekTimestamps)

        val weeklyCompletionPercentage = if (currentWeekCompletionPercentages.isNotEmpty()) {
            currentWeekCompletionPercentages.average().toFloat()
        } else {
            0f
        }

        // Calculate week number for today using the correct first day of week
        val weekNumber = WeekBoundaryUtils.getWeekNumber(today, firstDayOfWeek)

        return WeekInfo(
            dates = dates,
            timestamps = timestamps,
            formattedDates = formattedDates,
            formattedDayAbbreviations = formattedDayAbbreviations,
            dailyCompletionPercentages = dailyCompletionPercentages,
            weeklyCompletionPercentage = weeklyCompletionPercentage,
            weekNumber = weekNumber,
            firstDayOfWeek = firstDayOfWeek
        )
    }
    
    private fun getCurrentWeekStart(firstDayOfWeek: String): Long {
        val today = LocalDate.now()
        val weekStart = WeekBoundaryUtils.getWeekStart(today, firstDayOfWeek)
        return weekStart.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli()
    }

    private fun getCurrentWeekEnd(firstDayOfWeek: String): Long {
        val today = LocalDate.now()
        val weekEnd = WeekBoundaryUtils.getWeekEnd(today, firstDayOfWeek)
        return weekEnd.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli()
    }

    // Combine habits with their completion data for the last 15 days, respecting first day of week preference
    val uiState: StateFlow<MainUiState> =
        combine(
            habitRepository.getAllHabits(),
            userPreferencesRepository.firstDayOfWeek
        ) { habits, firstDayOfWeek ->
            val weekInfo = calculateWeekInfo(firstDayOfWeek)
            if (habits.isEmpty()) {
                MainUiState(
                    isLoading = false, 
                    currentWeekStart = getCurrentWeekStart(firstDayOfWeek),
                    weekInfo = weekInfo
                )
            } else {
                val habitIds = habits.map { it.id }
                // For now, create habits without completion data - we'll load completions separately
                val habitsWithCompletions = habits.map { habit ->
                    HabitWithCompletions(habit, emptyMap())
                }
                MainUiState(
                    habitsWithCompletions = habitsWithCompletions,
                    isLoading = false,
                    currentWeekStart = getCurrentWeekStart(firstDayOfWeek),
                    weekInfo = weekInfo
                )
            }
        }.stateIn(
            scope = viewModelScope,
            started = kotlinx.coroutines.flow.SharingStarted.WhileSubscribed(5000),
            initialValue = MainUiState(
                isLoading = true, 
                currentWeekStart = getCurrentWeekStart("SUNDAY"),
                weekInfo = calculateWeekInfo("SUNDAY")
            )
        )

    // Load completions separately and update UI state
    private val _completionsState = MutableStateFlow<Map<Long, Map<Long, Boolean>>>(emptyMap())
    private val _completionValuesState = MutableStateFlow<Map<Long, Map<Long, String>>>(emptyMap())

    // FIXED: Use switchMap pattern to ensure only one completion loading coroutine is active
    // This prevents multiple coroutines from interfering with each other and ensures proper UI updates
    private val completionsFlow = combine(
        habitRepository.getAllHabits(),
        userPreferencesRepository.firstDayOfWeek
    ) { habits, firstDayOfWeek ->
        if (habits.isNotEmpty()) {
            val habitIds = habits.map { it.id }
            // Calculate the date range for the last 15 days (today + 14 previous days)
            // This matches the UI display range and ensures all clickable dates have data
            val today = LocalDate.now()
            val fifteenDaysAgo = today.minusDays(14)
            val startDate = fifteenDaysAgo.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli()
            val endDate = today.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli()

            // Return the Flow directly - this ensures proper reactivity to database changes
            completionRepository.getCompletionsForHabitsInRange(
                habitIds = habitIds,
                startDate = startDate,
                endDate = endDate
            )
        } else {
            // Return empty flow when no habits exist
            flowOf(emptyList<Completion>())
        }
    }.flatMapLatest { it } // flatMapLatest ensures only the latest flow is active

    init {
        // Collect completions and update state - this ensures UI updates when database changes
        viewModelScope.launch {
            completionsFlow.collect { completions ->
                android.util.Log.d("BugFix", "Completions updated: ${completions.size} total completions")

                // FIXED: Create immutable maps to prevent state corruption
                // This ensures each habit's completion state is properly isolated
                val completionsMap = completions.groupBy { it.habitId }
                    .mapValues { (habitId, habitCompletions) ->
                        android.util.Log.d("BugFix", "Processing completions for habit $habitId: ${habitCompletions.size} completions")
                        habitCompletions.associate { completion ->
                            android.util.Log.d("BugFix", "Completion: habitId=${completion.habitId}, timestamp=${completion.timestamp}, value=${completion.value}")
                            completion.timestamp to true
                        }.toMap() // Create immutable map
                    }.toMap() // Create immutable outer map

                val completionValuesMap = completions.groupBy { it.habitId }
                    .mapValues { (habitId, habitCompletions) ->
                        habitCompletions.associate { completion ->
                            completion.timestamp to (completion.value ?: "")
                        }.toMap() // Create immutable map
                    }.toMap() // Create immutable outer map

                android.util.Log.d("BugFix", "Final completions map: $completionsMap")
                android.util.Log.d("BugFix", "Final completion values map: $completionValuesMap")

                // FIXED: Update states atomically to prevent race conditions
                // Log the state update to trace any issues
                android.util.Log.d("BugFix", "Updating completion states atomically")
                android.util.Log.d("BugFix", "New completions map keys: ${completionsMap.keys}")
                android.util.Log.d("BugFix", "New completion values map keys: ${completionValuesMap.keys}")

                _completionsState.value = completionsMap
                _completionValuesState.value = completionValuesMap

                android.util.Log.d("BugFix", "State update completed")
            }
        }
    }

    // Enhanced UI state that includes completion data, dialog state, and sorting
    val enhancedUiState: StateFlow<MainUiState> =
        combine(
            uiState,
            _completionsState,
            _completionValuesState,
            _dialogState,
            userPreferencesRepository.firstDayOfWeek,
            userPreferencesRepository.habitSortType,
            userPreferencesRepository.customHabitOrder
        ) { baseState, completionsMap, completionValuesMap, dialogState, firstDayOfWeek, sortType, customOrder ->
            if (baseState.habitsWithCompletions.isEmpty()) {
                baseState.copy(
                    weekInfo = calculateWeekInfo(firstDayOfWeek, emptyList()),
                    showMeasurableDialog = dialogState.showMeasurableDialog,
                    measurableDialogHabitId = dialogState.measurableDialogHabitId,
                    measurableDialogDate = dialogState.measurableDialogDate,
                    measurableDialogHabitName = dialogState.measurableDialogHabitName,
                    measurableDialogUnit = dialogState.measurableDialogUnit,
                    measurableDialogCurrentValue = dialogState.measurableDialogCurrentValue,
                    measurableDialogTargetValue = dialogState.measurableDialogTargetValue,
                    measurableDialogTargetType = dialogState.measurableDialogTargetType,
                    currentSortType = sortType
                )
            } else {
                val enhancedHabits = baseState.habitsWithCompletions.map { habitWithCompletions ->
                    val completions = completionsMap[habitWithCompletions.habit.id] ?: emptyMap()
                    val completionValues = completionValuesMap[habitWithCompletions.habit.id] ?: emptyMap()

                    // FIXED: Add detailed logging to trace completion mapping
                    android.util.Log.d("BugFix", "Enhancing habit ${habitWithCompletions.habit.id} (${habitWithCompletions.habit.name})")
                    android.util.Log.d("BugFix", "  - Completions: ${completions.keys}")
                    android.util.Log.d("BugFix", "  - Completion values: ${completionValues.keys}")

                    val scheduledDays = calculateScheduledDays(
                        habitWithCompletions.habit,
                        baseState.weekInfo.timestamps
                    )
                    val streak = calculateCurrentStreak(
                        habitWithCompletions.habit,
                        completions,
                        completionValues,
                        baseState.weekInfo.timestamps
                    )
                    habitWithCompletions.copy(
                        completions = completions,
                        completionValues = completionValues,
                        currentStreak = streak,
                        scheduledDays = scheduledDays
                    )
                }

                // Apply smart filtering - only show habits scheduled for at least one visible day
                val filteredHabits = filterScheduledHabits(enhancedHabits)

                // Apply sorting
                val sortedHabits = runBlocking {
                    sortHabits(filteredHabits, sortType, customOrder)
                }

                val updatedWeekInfo = calculateWeekInfo(firstDayOfWeek, sortedHabits)
                baseState.copy(
                    habitsWithCompletions = sortedHabits,
                    weekInfo = updatedWeekInfo,
                    showMeasurableDialog = dialogState.showMeasurableDialog,
                    measurableDialogHabitId = dialogState.measurableDialogHabitId,
                    measurableDialogDate = dialogState.measurableDialogDate,
                    measurableDialogHabitName = dialogState.measurableDialogHabitName,
                    measurableDialogUnit = dialogState.measurableDialogUnit,
                    measurableDialogCurrentValue = dialogState.measurableDialogCurrentValue,
                    measurableDialogTargetValue = dialogState.measurableDialogTargetValue,
                    measurableDialogTargetType = dialogState.measurableDialogTargetType,
                    currentSortType = sortType
                )
            }
        }.stateIn(
            scope = viewModelScope,
            started = kotlinx.coroutines.flow.SharingStarted.WhileSubscribed(5000),
            initialValue = MainUiState(
                isLoading = true,
                currentWeekStart = getCurrentWeekStart("SUNDAY"),
                weekInfo = calculateWeekInfo("SUNDAY")
            )
        )


    /**
     * Toggles the completion status of a Yes/No habit for a specific date.
     * If a completion exists, it will be deleted. If not, a new completion will be created.
     */
    fun toggleCompletion(habitId: Long, date: Long) {
        // Empty - old logic removed
    }

    /**
     * Shows the numerical input dialog for a measurable habit
     */
    fun showMeasurableHabitDialog(habitId: Long, date: Long) {
        // Empty - old logic removed
    }

    /**
     * Hides the numerical input dialog
     */
    fun hideMeasurableHabitDialog() {
        _dialogState.value = _dialogState.value.copy(
            showMeasurableDialog = false,
            measurableDialogHabitId = 0L,
            measurableDialogDate = 0L,
            measurableDialogHabitName = "",
            measurableDialogUnit = "",
            measurableDialogCurrentValue = "",
            measurableDialogTargetValue = 0.0,
            measurableDialogTargetType = "at least"
        )
    }

    /**
     * Saves the measurable habit completion with the given value
     */
    fun saveMeasurableHabitCompletion(habitId: Long, date: Long, value: Double) {
        viewModelScope.launch {
            try {
                // Normalize the date to start of day
                val dayLength = 24 * 60 * 60 * 1000L
                val dayStart = (date / dayLength) * dayLength

                // Check if completion already exists for this habit and date
                val existingCompletion = completionRepository.getCompletionForHabitAndDate(habitId, dayStart)

                if (existingCompletion != null) {
                    // Update existing completion
                    val updatedCompletion = existingCompletion.copy(value = value.toString())
                    completionRepository.updateCompletion(updatedCompletion)
                } else {
                    // Create new completion
                    val newCompletion = Completion(
                        habitId = habitId,
                        timestamp = dayStart,
                        value = value.toString()
                    )
                    completionRepository.insertCompletion(newCompletion)
                }

                // Hide the dialog
                hideMeasurableHabitDialog()
            } catch (e: Exception) {
                android.util.Log.e("MainViewModel", "Error in saveMeasurableHabitCompletion", e)
            }
        }
    }

    /**
     * Updates the current value in the dialog
     */
    fun updateMeasurableDialogValue(value: String) {
        _dialogState.value = _dialogState.value.copy(
            measurableDialogCurrentValue = value
        )
    }

    /**
     * Unified function to handle all cell taps for both Yes/No and Measurable habits.
     * This is the new, clean implementation that replaces the old buggy logic.
     */
    fun onCellClick(habitId: Long, habitType: HabitType, date: Long) {
        viewModelScope.launch {
            try {
                // Normalize the date to start of day
                val dayLength = 24 * 60 * 60 * 1000L
                val dayStart = (date / dayLength) * dayLength

                when (habitType) {
                    HabitType.YES_NO -> {
                        // For Yes/No habits: check if completion exists, toggle accordingly
                        val existingCompletion = completionRepository.getCompletionForHabitAndDate(habitId, dayStart)

                        if (existingCompletion != null) {
                            // Completion exists, delete it
                            completionRepository.deleteCompletion(existingCompletion)
                        } else {
                            // No completion exists, create a new one
                            val newCompletion = Completion(
                                habitId = habitId,
                                timestamp = dayStart,
                                value = null // null for Yes/No habits
                            )
                            completionRepository.insertCompletion(newCompletion)
                        }
                    }

                    HabitType.NUMERICAL -> {
                        // For Measurable habits: trigger UI event to open NumericalInputDialog
                        showMeasurableHabitDialogNew(habitId, dayStart)
                    }
                }
            } catch (e: Exception) {
                android.util.Log.e("MainViewModel", "Error in onCellClick for habit $habitId", e)
            }
        }
    }

    /**
     * New implementation of showMeasurableHabitDialog for the rebuilt logic
     */
    private suspend fun showMeasurableHabitDialogNew(habitId: Long, date: Long) {
        try {
            // Get the habit to retrieve its name and unit
            val habit = habitRepository.getHabitByIdSync(habitId)

            if (habit != null && habit.habitType == HabitType.NUMERICAL) {
                // Get existing completion value if any
                val existingCompletion = completionRepository.getCompletionForHabitAndDate(habitId, date)
                val currentValue = existingCompletion?.value ?: "0"

                // Convert target type to display string
                val targetTypeText = when (habit.numericalHabitType) {
                    com.example.habits9.data.NumericalHabitType.AT_LEAST -> "at least"
                    com.example.habits9.data.NumericalHabitType.AT_MOST -> "at most"
                }

                // Update dialog state
                val newDialogState = _dialogState.value.copy(
                    showMeasurableDialog = true,
                    measurableDialogHabitId = habitId,
                    measurableDialogDate = date,
                    measurableDialogHabitName = habit.name,
                    measurableDialogUnit = habit.unit,
                    measurableDialogCurrentValue = currentValue,
                    measurableDialogTargetValue = habit.targetValue,
                    measurableDialogTargetType = targetTypeText
                )

                _dialogState.value = newDialogState
            }
        } catch (e: Exception) {
            android.util.Log.e("MainViewModel", "Error in showMeasurableHabitDialogNew", e)
        }
    }

    /**
     * Update the habit sort type preference
     */
    fun updateSortType(sortType: HabitSortType) {
        viewModelScope.launch {
            userPreferencesRepository.updateHabitSortType(sortType)
        }
    }

    /**
     * Update the custom habit order
     */
    fun updateCustomHabitOrder(habitUuids: List<String>) {
        viewModelScope.launch {
            userPreferencesRepository.updateCustomHabitOrder(habitUuids)
        }
    }

    /**
     * Sort habits based on the current sort type
     */
    private suspend fun sortHabits(
        habits: List<HabitWithCompletions>,
        sortType: HabitSortType,
        customOrder: List<String>
    ): List<HabitWithCompletions> {
        return when (sortType) {
            HabitSortType.BY_NAME -> {
                habits.sortedBy { it.habit.name.lowercase() }
            }
            HabitSortType.BY_SECTION -> {
                // Get all sections to create a mapping
                val sections = habitSectionRepository.getAllSectionsSync()
                val sectionMap = sections.associateBy { it.id }

                habits.sortedWith(compareBy<HabitWithCompletions> { habitWithCompletions ->
                    // First sort by section name (or "No Section" for habits without a section)
                    val sectionName = sectionMap[habitWithCompletions.habit.color]?.name ?: "No Section"
                    sectionName.lowercase()
                }.thenBy { habitWithCompletions ->
                    // Then sort by habit name within each section
                    habitWithCompletions.habit.name.lowercase()
                })
            }
            HabitSortType.CUSTOM_ORDER -> {
                if (customOrder.isEmpty()) {
                    // If no custom order is set, fall back to position-based sorting
                    habits.sortedBy { it.habit.position }
                } else {
                    // Sort by custom order, with unordered habits at the end
                    val orderMap = customOrder.withIndex().associate { (index, uuid) -> uuid to index }
                    habits.sortedWith(compareBy<HabitWithCompletions> { habitWithCompletions ->
                        orderMap[habitWithCompletions.habit.uuid] ?: Int.MAX_VALUE
                    }.thenBy { it.habit.position })
                }
            }
        }
    }
}