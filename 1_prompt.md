# Prompt for Developer: Implement Habit Sort Feature on Home Screen

### **Objective**

To implement a comprehensive sorting feature for habits on the main home screen. This feature will allow users to organize their habits by name, by section, or through a custom manual order using drag and drop.

### **Context**

The home screen currently lacks sorting capabilities. To improve user control and organization, we are introducing a sort feature. The primary challenge is adapting this functionality to the home screen's existing table-based layout. This implementation will involve adding a new UI entry point (a sort icon), building the sorting logic, and creating a dedicated interface for manual drag-and-drop reordering.

---

### **Part 1: Detailed Implementation Plan**

#### **Subtask 1: Update Home Screen UI**

- **Add Sort Icon:**
  - In the screen's header area, introduce a new "sort" icon.
  - Position this icon between the existing kebab menu icon and the primary "add habit" button.
- **Implement Sort Menu:**
  - On tapping the sort icon, present a menu (dropdown or bottom sheet) with the following options:
    - `Sort by Name`
    - `Sort by Section`
    - `Custom Order (Drag & Drop)`
  - **Active State Feedback:** The menu must visually indicate the currently active sort order. Use a checkmark icon (`✓`) or a similar clear indicator next to the active option.

#### **Subtask 2: Implement Automatic Sorting Logic**

- **Sort by Name:**
  - When this option is selected, the habits displayed on the home screen should immediately re-render, sorted alphabetically (A-Z) by name.
  - The sorting operation should be performed directly on the home screen without navigating the user to a different interface.
- **Sort by Section:**
  - When this option is selected, the habits should be grouped by their assigned section. Within each section group, habits should be sorted alphabetically by name.
  - This sorting should also occur directly on the home screen.

#### **Subtask 3: Implement Custom Drag & Drop Sorting**

- **Create Dedicated Sorting Interface:**
  - When the user selects `"Custom Order (Drag & Drop)"`, navigate them to a new, purpose-built screen.
  - This screen will display only a simple, vertical list of habit names (without the home screen's table structure), optimized for reordering.
- **Implement Drag & Drop Functionality:**
  - The user must be able to press and hold a habit to "lift" it, then drag it to a new position in the list.
  - Provide clear visual feedback while a habit is being dragged.
- **Handle Empty State:**
  - If a user navigates to this screen when they have no habits, the interface must not be blank. It should display a helpful message: _"You have no habits to organize. Go back and add a new habit to get started!"_

#### **Subtask 4: Ensure Persistence**

- **Persist Sort Preference:** The user's last selected sort choice (Name, Section, or Custom) must be saved locally. When the application is next launched, habits must load in the order corresponding to this saved preference.
- **Persist Custom Order:** Any changes made on the "Custom Order" screen must be saved and immediately reflected on the main home screen once the user returns.

---

### **Part 2: Verification Steps**

Please verify the implementation by following these steps:

1.  **UI Verification:** Check that the sort icon is present and correctly positioned. Tap it to confirm the menu appears with the three specified options.
2.  **"Sort by Name" Test:** Select it. Verify habits reorder alphabetically on the home screen. Check that the menu correctly indicates this is the active state. Relaunch the app and confirm the sort order is preserved.
3.  **"Sort by Section" Test:** Select it. Verify habits are grouped by section and sorted alphabetically within those groups. Check for the active state indicator in the menu. Relaunch and confirm persistence.
4.  **"Custom Order" Test:** Select it. Verify navigation to the new screen. Reorder at least two habits, save/return, and confirm the home screen reflects the new manual order. Check for the active state indicator. Relaunch and confirm the custom order is preserved.
5.  **Empty State Test:** With zero habits, navigate to the custom order screen and verify the empty state message is displayed.

---

## ⚙️ Mandatory Development Guidelines

These practices must be followed during all phases of development—planning, implementation, and review.

### 1. Refer to the Style Guide

Before starting any feature:

- Always consult the **style guide** for rules related to UI/UX, layout, naming conventions, spacing, colors, and design patterns.
- The style guide is located in the **`style.md` file in the root folder`**.
- It is the **single source of truth** for styling decisions.

### 2. Study the Reference Project

Prior to implementation:

- Review the **reference project** located in the `uhabits-dev` folder (in the root directory).
- Understand how similar features have been approached to maintain consistency and avoid duplications or contradictions.
- The reference project serves as a **blueprint** for implementation.
- This step is mandatory. **Do not proceed to implementation without this step.**

### 3. Understand the Existing Project Structure

Before writing any code:

- Spend time exploring and understanding how the current system is structured.
- Even for new features, existing components or utility functions may be reusable.
- Integrate changes **cleanly into the existing architecture** instead of creating disconnected code.

### 4. Maintain a Clean Codebase

After implementing features:

- Remove any temporary, test, or duplicate files, folders, routes, or unused components that were created during development.
- Keep the codebase **organized and clutter-free**.

### 5. Pause If There Is Any Confusion

If at any point the requirements are unclear:

- **Do not proceed** based on assumptions.
- Immediately pause and seek clarification either from the project lead or directly from me.
- It is better to get clarity than to redo or fix avoidable mistakes later.

### 6. Remove Unused Old Implementations

As part of final review:

- Identify and delete **any old, unused code** that was implemented earlier but is no longer in use.
- This includes obsolete routes, modules, features, or legacy logic.

---

By adhering to these principles and mandatory practices, we ensure clarity, precision, and high-quality development that scales well over time.
