package com.example.habits9.ui.managesections

import androidx.compose.animation.animateContentSize
import androidx.compose.animation.core.animateDpAsState
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectDragGesturesAfterLongPress
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.DragIndicator
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.scale
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalHapticFeedback
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.zIndex
import androidx.hilt.navigation.compose.hiltViewModel
import com.example.habits9.data.HabitSection
import kotlinx.coroutines.launch

// Design System Colors - Dark Theme
private val DarkBackground = Color(0xFF121826)
private val SurfaceVariantDark = Color(0xFF1A202C)
private val TextPrimary = Color(0xFFE2E8F0)
private val TextSecondary = Color(0xFFA0AEC0)
private val AccentPrimary = Color(0xFF81E6D9)
private val DividerColor = Color(0xFF2D3748)

// Predefined colors for section selection
private val SectionColors = listOf(
    Color(0xFF81E6D9), // Teal
    Color(0xFF68D391), // Green
    Color(0xFF63B3ED), // Blue
    Color(0xFFF687B3), // Pink
    Color(0xFFFBB6CE), // Light Pink
    Color(0xFFFBD38D), // Orange
    Color(0xFFBEE3F8), // Light Blue
    Color(0xFFC6F6D5), // Light Green
    Color(0xFFE9D8FD), // Purple
    Color(0xFFFED7D7)  // Red
)

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ManageSectionsScreen(
    onBackClick: () -> Unit = {},
    viewModel: ManageSectionsViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    val coroutineScope = rememberCoroutineScope()
    val listState = rememberLazyListState()
    val hapticFeedback = LocalHapticFeedback.current
    
    // State for smooth drag-and-drop with ghost placeholder
    var draggedItemIndex by remember { mutableStateOf<Int?>(null) }
    var dragOffset by remember { mutableStateOf(Offset.Zero) }
    var dropTargetIndex by remember { mutableStateOf<Int?>(null) }
    var draggedSection by remember { mutableStateOf<HabitSection?>(null) }

    Scaffold(
        containerColor = DarkBackground,
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = "Manage Sections",
                        color = TextPrimary,
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Medium
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onBackClick) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "Back",
                            tint = TextPrimary
                        )
                    }
                },
                actions = {
                    IconButton(onClick = { viewModel.showCreateDialog() }) {
                        Icon(
                            imageVector = Icons.Default.Add,
                            contentDescription = "Add Section",
                            tint = TextPrimary
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = SurfaceVariantDark
                )
            )
        }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            if (uiState.isLoading) {
                CircularProgressIndicator(
                    modifier = Modifier.align(Alignment.Center),
                    color = AccentPrimary
                )
            } else if (uiState.sections.isEmpty()) {
                Text(
                    text = "No sections created yet.",
                    color = TextSecondary,
                    fontSize = 16.sp,
                    modifier = Modifier.align(Alignment.Center)
                )
            } else {
                LazyColumn(
                    state = listState,
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(horizontal = 16.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp),
                    contentPadding = PaddingValues(vertical = 16.dp)
                ) {
                    uiState.sections.forEachIndexed { index, section ->
                        val isDraggingThisItem = index == draggedItemIndex
                        val shouldShowGhostBefore = dropTargetIndex == index && draggedItemIndex != null && draggedItemIndex != index
                        
                        // Show ghost placeholder before this item if needed
                        if (shouldShowGhostBefore && draggedSection != null) {
                            item(key = "ghost_${draggedSection!!.id}") {
                                GhostPlaceholder(
                                    section = draggedSection!!,
                                    modifier = Modifier.animateContentSize()
                                )
                            }
                        }
                        
                        // Show the actual section item
                        item(key = "section_${section.id}") {
                        
                        SectionListItem(
                            section = section,
                            isDragging = isDraggingThisItem,
                            dragOffset = if (isDraggingThisItem) dragOffset else Offset.Zero,
                            onSectionClick = { 
                                if (!isDraggingThisItem) {
                                    viewModel.showEditDialog(section) 
                                }
                            },
                            onDeleteClick = { 
                                if (!isDraggingThisItem) {
                                    viewModel.showDeleteDialog(section) 
                                }
                            },
                            onDragStart = {
                                draggedItemIndex = index
                                draggedSection = section
                                dragOffset = Offset.Zero
                                hapticFeedback.performHapticFeedback(HapticFeedbackType.LongPress)
                            },
                            onDrag = { dragAmount ->
                                dragOffset += dragAmount
                                
                                // Calculate drop target based on drag position
                                val visibleItems = listState.layoutInfo.visibleItemsInfo
                                val currentItemInfo = visibleItems.find { it.index == index }
                                
                                if (currentItemInfo != null) {
                                    val currentItemCenter = currentItemInfo.offset + currentItemInfo.size / 2
                                    val draggedItemCenter = currentItemCenter + dragOffset.y
                                    
                                    // Find the item we're hovering over
                                    var newDropTarget: Int? = null
                                    for (item in visibleItems) {
                                        val itemTop = item.offset
                                        val itemBottom = item.offset + item.size
                                        val itemCenter = item.offset + item.size / 2
                                        
                                        if (draggedItemCenter >= itemTop && draggedItemCenter <= itemBottom) {
                                            // Determine if we should insert before or after this item
                                            newDropTarget = if (draggedItemCenter < itemCenter) {
                                                item.index
                                            } else {
                                                (item.index + 1).coerceAtMost(uiState.sections.size - 1)
                                            }
                                            break
                                        }
                                    }
                                    
                                    if (newDropTarget != draggedItemIndex && newDropTarget != dropTargetIndex) {
                                        dropTargetIndex = newDropTarget
                                    }
                                }
                            },
                            onDragEnd = {
                                val fromIndex = draggedItemIndex
                                val toIndex = dropTargetIndex
                                
                                // Reset drag state
                                draggedItemIndex = null
                                draggedSection = null
                                dragOffset = Offset.Zero
                                dropTargetIndex = null
                                
                                // Trigger haptic feedback for drop
                                hapticFeedback.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                                
                                // Perform the move if valid
                                if (fromIndex != null && toIndex != null && fromIndex != toIndex) {
                                    viewModel.onSectionMoved(fromIndex, toIndex)
                                }
                            }
                        )
                        }
                    }
                    
                    // Show ghost placeholder at the end if needed
                    if (dropTargetIndex == uiState.sections.size && draggedSection != null) {
                        item(key = "ghost_end_${draggedSection!!.id}") {
                            GhostPlaceholder(
                                section = draggedSection!!,
                                modifier = Modifier.animateContentSize()
                            )
                        }
                    }
                }
            }
        }
    }

    // Create Section Dialog
    if (uiState.showCreateDialog) {
        CreateSectionDialog(
            onDismiss = { viewModel.hideCreateDialog() },
            onConfirm = { name, color -> viewModel.createSection(name, color) }
        )
    }

    // Edit Section Dialog
    uiState.editingSection?.let { editingSection ->
        if (uiState.showEditDialog) {
            EditSectionDialog(
                section = editingSection,
                onDismiss = { viewModel.hideEditDialog() },
                onConfirm = { name, color -> viewModel.updateSection(name, color) }
            )
        }
    }

    // Delete Confirmation Dialog
    uiState.deletingSection?.let { deletingSection ->
        if (uiState.showDeleteDialog) {
            DeleteSectionDialog(
                sectionName = deletingSection.name,
                onDismiss = { viewModel.hideDeleteDialog() },
                onConfirm = { viewModel.deleteSection() }
            )
        }
    }
}

@Composable
private fun GhostPlaceholder(
    section: HabitSection,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .alpha(0.5f), // Semi-transparent ghost effect
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = SurfaceVariantDark.copy(alpha = 0.3f)
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Drag handle (non-interactive for ghost)
            Icon(
                imageVector = Icons.Default.DragIndicator,
                contentDescription = null,
                tint = TextSecondary.copy(alpha = 0.5f),
                modifier = Modifier.size(24.dp)
            )
            
            Spacer(modifier = Modifier.width(12.dp))
            
            // Color circle
            Box(
                modifier = Modifier
                    .size(24.dp)
                    .background(
                        color = Color(section.color).copy(alpha = 0.5f),
                        shape = CircleShape
                    )
            )
            
            Spacer(modifier = Modifier.width(16.dp))
            
            // Section name
            Text(
                text = section.name,
                color = TextPrimary.copy(alpha = 0.5f),
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                modifier = Modifier.weight(1f)
            )
            
            // Delete icon (non-interactive for ghost)
            Icon(
                imageVector = Icons.Default.Delete,
                contentDescription = null,
                tint = TextSecondary.copy(alpha = 0.3f),
                modifier = Modifier.size(20.dp)
            )
        }
    }
}

@Composable
private fun SectionListItem(
    section: HabitSection,
    isDragging: Boolean = false,
    dragOffset: Offset = Offset.Zero,
    onSectionClick: () -> Unit = {},
    onDeleteClick: () -> Unit = {},
    onDragStart: () -> Unit = {},
    onDrag: (Offset) -> Unit = {},
    onDragEnd: () -> Unit = {}
) {
    // Smooth animations for visual feedback
    val elevation by animateDpAsState(
        targetValue = if (isDragging) 12.dp else 2.dp,
        label = "elevation"
    )
    val scale by animateFloatAsState(
        targetValue = if (isDragging) 1.05f else 1f,
        label = "scale"
    )
    val alpha by animateFloatAsState(
        targetValue = if (isDragging) 0.95f else 1f,
        label = "alpha"
    )
    
    Column {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .clickable(enabled = !isDragging) { onSectionClick() }
                .shadow(elevation = elevation, shape = RoundedCornerShape(12.dp))
                .graphicsLayer {
                    // Apply smooth finger tracking
                    translationX = dragOffset.x
                    translationY = dragOffset.y
                    scaleX = scale
                    scaleY = scale
                    this.alpha = alpha
                }
                .zIndex(if (isDragging) 10f else 1f),
            shape = RoundedCornerShape(12.dp),
            colors = CardDefaults.cardColors(
                containerColor = if (isDragging) SurfaceVariantDark.copy(alpha = 0.9f) else SurfaceVariantDark
            )
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Drag handle with enhanced visual feedback
                Icon(
                    imageVector = Icons.Default.DragIndicator,
                    contentDescription = "Drag to reorder",
                    tint = if (isDragging) AccentPrimary else TextSecondary,
                    modifier = Modifier
                        .size(24.dp)
                        .pointerInput(section.id) {
                            detectDragGesturesAfterLongPress(
                                onDragStart = { 
                                    onDragStart()
                                },
                                onDrag = { change, dragAmount ->
                                    change.consume()
                                    onDrag(dragAmount)
                                },
                                onDragEnd = { 
                                    onDragEnd() 
                                },
                                onDragCancel = { 
                                    onDragEnd() 
                                }
                            )
                        }
                )
                
                Spacer(modifier = Modifier.width(12.dp))
                
                // Color circle with enhanced visual feedback
                Box(
                    modifier = Modifier
                        .size(24.dp)
                        .background(
                            color = Color(section.color),
                            shape = CircleShape
                        )
                        .then(
                            if (isDragging) {
                                Modifier.shadow(4.dp, CircleShape)
                            } else {
                                Modifier
                            }
                        )
                )
                
                Spacer(modifier = Modifier.width(16.dp))
                
                // Section name with enhanced typography
                Text(
                    text = section.name,
                    color = if (isDragging) AccentPrimary else TextPrimary,
                    fontSize = 16.sp,
                    fontWeight = if (isDragging) FontWeight.SemiBold else FontWeight.Medium,
                    modifier = Modifier.weight(1f)
                )
                
                // Delete icon (disabled during drag)
                IconButton(
                    onClick = onDeleteClick,
                    enabled = !isDragging,
                    modifier = Modifier
                        .size(40.dp)
                        .alpha(if (isDragging) 0.5f else 1f)
                ) {
                    Icon(
                        imageVector = Icons.Default.Delete,
                        contentDescription = "Delete Section",
                        tint = TextSecondary,
                        modifier = Modifier.size(20.dp)
                    )
                }
            }
        }
    }
}

@Composable
private fun CreateSectionDialog(
    onDismiss: () -> Unit,
    onConfirm: (String, Int) -> Unit
) {
    var sectionName by remember { mutableStateOf("") }
    var selectedColorIndex by remember { mutableStateOf(0) }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "Create New Section",
                color = TextPrimary,
                fontWeight = FontWeight.Medium
            )
        },
        text = {
            Column {
                // Name input field
                OutlinedTextField(
                    value = sectionName,
                    onValueChange = { sectionName = it },
                    label = { Text("Section Name", color = TextSecondary) },
                    placeholder = { Text("e.g. Morning", color = TextSecondary) },
                    modifier = Modifier.fillMaxWidth(),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedTextColor = TextPrimary,
                        unfocusedTextColor = TextPrimary,
                        focusedBorderColor = AccentPrimary,
                        unfocusedBorderColor = DividerColor,
                        cursorColor = AccentPrimary
                    )
                )

                Spacer(modifier = Modifier.height(16.dp))

                // Color picker
                Text(
                    text = "Color",
                    color = TextSecondary,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                LazyRow(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    itemsIndexed(SectionColors) { index, color ->
                        Box(
                            modifier = Modifier
                                .size(40.dp)
                                .background(
                                    color = color,
                                    shape = CircleShape
                                )
                                .clickable { selectedColorIndex = index }
                                .then(
                                    if (selectedColorIndex == index) {
                                        Modifier.background(
                                            color = Color.White.copy(alpha = 0.3f),
                                            shape = CircleShape
                                        )
                                    } else {
                                        Modifier
                                    }
                                )
                        )
                    }
                }
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    // Convert the selected color to an Int for storage
                    val colorInt = SectionColors[selectedColorIndex].toArgb()
                    onConfirm(sectionName, colorInt)
                },
                enabled = sectionName.isNotBlank()
            ) {
                Text(
                    text = "Create",
                    color = if (sectionName.isNotBlank()) AccentPrimary else TextSecondary
                )
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Cancel", color = TextSecondary)
            }
        },
        containerColor = SurfaceVariantDark
    )
}

@Composable
private fun EditSectionDialog(
    section: HabitSection,
    onDismiss: () -> Unit,
    onConfirm: (String, Int) -> Unit
) {
    var sectionName by remember { mutableStateOf(section.name) }
    
    // Find the closest matching color in our predefined colors
    val initialColorIndex = remember {
        val sectionColor = Color(section.color)
        SectionColors.indexOfFirst { it.toArgb() == section.color }
            .takeIf { it >= 0 } ?: 0 // Default to first color if not found
    }
    
    var selectedColorIndex by remember { mutableStateOf(initialColorIndex) }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "Edit Section",
                color = TextPrimary,
                fontWeight = FontWeight.Medium
            )
        },
        text = {
            Column {
                // Name input field
                OutlinedTextField(
                    value = sectionName,
                    onValueChange = { sectionName = it },
                    label = { Text("Section Name", color = TextSecondary) },
                    placeholder = { Text("e.g. Morning", color = TextSecondary) },
                    modifier = Modifier.fillMaxWidth(),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedTextColor = TextPrimary,
                        unfocusedTextColor = TextPrimary,
                        focusedBorderColor = AccentPrimary,
                        unfocusedBorderColor = DividerColor,
                        cursorColor = AccentPrimary
                    )
                )

                Spacer(modifier = Modifier.height(16.dp))

                // Color picker
                Text(
                    text = "Color",
                    color = TextSecondary,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                LazyRow(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    itemsIndexed(SectionColors) { index, color ->
                        Box(
                            modifier = Modifier
                                .size(40.dp)
                                .background(
                                    color = color,
                                    shape = CircleShape
                                )
                                .clickable { selectedColorIndex = index }
                                .then(
                                    if (selectedColorIndex == index) {
                                        Modifier.background(
                                            color = Color.White.copy(alpha = 0.3f),
                                            shape = CircleShape
                                        )
                                    } else {
                                        Modifier
                                    }
                                )
                        )
                    }
                }
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    // Convert the selected color to an Int for storage
                    val colorInt = SectionColors[selectedColorIndex].toArgb()
                    onConfirm(sectionName, colorInt)
                },
                enabled = sectionName.isNotBlank()
            ) {
                Text(
                    text = "Save",
                    color = if (sectionName.isNotBlank()) AccentPrimary else TextSecondary
                )
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Cancel", color = TextSecondary)
            }
        },
        containerColor = SurfaceVariantDark
    )
}

@Composable
private fun DeleteSectionDialog(
    sectionName: String,
    onDismiss: () -> Unit,
    onConfirm: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "Delete Section?",
                color = TextPrimary,
                fontWeight = FontWeight.Medium
            )
        },
        text = {
            Text(
                text = "Are you sure you want to delete '$sectionName'? This action cannot be undone.",
                color = TextSecondary,
                fontSize = 14.sp
            )
        },
        confirmButton = {
            TextButton(onClick = onConfirm) {
                Text(
                    text = "Delete",
                    color = Color(0xFFFF6B6B) // Red color for destructive action
                )
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Cancel", color = TextSecondary)
            }
        },
        containerColor = SurfaceVariantDark
    )
}