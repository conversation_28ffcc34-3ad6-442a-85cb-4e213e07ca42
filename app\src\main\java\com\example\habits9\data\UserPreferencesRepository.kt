package com.example.habits9.data

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

// Extension property to create DataStore instance
private val Context.dataStore: DataStore<Preferences> by preferencesDataStore(name = "user_preferences")

@Singleton
class UserPreferencesRepository @Inject constructor(
    @ApplicationContext private val context: Context
) {
    private object PreferencesKeys {
        val FIRST_DAY_OF_WEEK = stringPreferencesKey("first_day_of_week")
    }
    
    /**
     * Get the first day of week preference as a Flow
     * @return Flow<String> - "SUNDAY" or "MONDAY", defaults to "SUNDAY"
     */
    val firstDayOfWeek: Flow<String> = context.dataStore.data.map { preferences ->
        preferences[PreferencesKeys.FIRST_DAY_OF_WEEK] ?: "SUNDAY"
    }
    
    /**
     * Update the first day of week preference
     * @param dayOfWeek String - "SUNDAY" or "MONDAY"
     */
    suspend fun updateFirstDayOfWeek(dayOfWeek: String) {
        context.dataStore.edit { preferences ->
            preferences[PreferencesKeys.FIRST_DAY_OF_WEEK] = dayOfWeek
        }
    }
}